Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x1FE8E
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210286019, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8E80  000210068E24 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9160  00021006A225 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE50040000 ntdll.dll
7FFE4F0D0000 KERNEL32.DLL
7FFE4D490000 KERNELBASE.dll
7FFE4DE00000 USER32.dll
7FFE4DC80000 win32u.dll
7FFE4E810000 GDI32.dll
7FFE4D900000 gdi32full.dll
7FFE4D1D0000 msvcp_win.dll
7FFE4DCB0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE4E300000 advapi32.dll
7FFE4F300000 msvcrt.dll
7FFE4E840000 sechost.dll
7FFE4E1C0000 RPCRT4.dll
7FFE4C7C0000 CRYPTBASE.DLL
7FFE4D860000 bcryptPrimitives.dll
7FFE4FB10000 IMM32.DLL
