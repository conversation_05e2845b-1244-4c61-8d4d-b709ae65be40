"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";

interface BlogDetailHeroProps {
    title: string;
    subtitle: string;
    date: string;
    heroImage: string;
}

const BlogDetailHero = ({
    title,
    subtitle,
    date,
    heroImage,
}: BlogDetailHeroProps) => {
    return (
        <section className="w-full bg-white">
            {/* Full width hero container with background image */}
            <div className="relative w-full h-[80vh] overflow-hidden flex items-center justify-center">
                {/* Background Image */}
                <div className="absolute inset-0 z-0">
                    <Image
                        src={heroImage}
                        alt={title}
                        fill
                        className="object-cover"
                        priority
                    />
                    {/* Dark overlay for better text readability */}
                    <div className="absolute inset-0 bg-black/40"></div>
                </div>

                {/* Content */}
                <div className="relative z-10 px-4 sm:px-6 md:px-8 lg:px-12 text-center text-white w-full">
                    <div className="max-w-7xl mx-auto">
                        <div className="max-w-4xl mx-auto">
                            {/* Press Release Label */}
                            <motion.div
                                className="mb-4 sm:mb-6"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6 }}
                            >
                                <span className="inline-block bg-white/20 backdrop-blur-sm text-white text-xs font-medium uppercase tracking-wider px-4 py-2 rounded-full border border-white/30">
                                    Press Release
                                </span>
                            </motion.div>

                            {/* Title */}
                            <motion.h1
                                className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-bold text-white mb-4 sm:mb-6 leading-tight"
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                            >
                                {title}
                            </motion.h1>

                            {/* Date */}
                            <motion.div
                                className="mb-4"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.3 }}
                            >
                                <span className="text-white/90 text-sm sm:text-base md:text-lg lg:text-xl font-light max-w-xs sm:max-w-md md:max-w-2xl lg:max-w-3xl mx-auto leading-relaxed tracking-wide">
                                    {date}
                                </span>
                            </motion.div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Back to News Link - Outside the hero container */}
            <div className="bg-white py-8 md:py-12">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.4 }}
                        >
                            <Link
                                href="/blog"
                                className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 text-sm font-medium uppercase tracking-wide transition-colors duration-300"
                            >
                                <ArrowLeft className="w-4 h-4" />
                                BACK TO OUR NEWS
                            </Link>
                        </motion.div>
                    </div>
                </div>
            </div>

            {/* Subtitle Section */}
            <div className="bg-white pb-8 md:pb-12">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.5 }}
                        >
                            <h3 className="text-gray-800 font-markazi-text text-xl md:text-2xl lg:text-3xl font-normal leading-relaxed text-center">
                                {subtitle}
                            </h3>
                        </motion.div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default BlogDetailHero;
